<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP通知服务器状态</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.online {
            background-color: #d4edda;
            color: #155724;
        }
        .status.offline {
            background-color: #f8d7da;
            color: #721c24;
        }
        .notification {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .notification .title {
            font-weight: bold;
            color: #333;
        }
        .notification .message {
            color: #666;
            margin: 5px 0;
        }
        .notification .time {
            color: #999;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>MCP通知服务器状态</h1>
        <div id="serverStatus" class="status">
            正在检查服务器状态...
        </div>
        <h2>通知历史</h2>
        <div id="notificationHistory"></div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        const socket = io();
        const serverStatus = document.getElementById('serverStatus');
        const notificationHistory = document.getElementById('notificationHistory');

        // 检查服务器状态
        function checkServerStatus() {
            fetch('/health')
                .then(response => response.json())
                .then(data => {
                    serverStatus.className = 'status online';
                    serverStatus.textContent = '服务器在线';
                })
                .catch(error => {
                    serverStatus.className = 'status offline';
                    serverStatus.textContent = '服务器离线';
                });
        }

        // 定期检查服务器状态
        setInterval(checkServerStatus, 5000);
        checkServerStatus();

        // 监听通知
        socket.on('notification', (data) => {
            const notification = document.createElement('div');
            notification.className = 'notification';
            notification.innerHTML = `
                <div class="title">${data.title}</div>
                <div class="message">${data.message}</div>
                <div class="time">${new Date(data.timestamp).toLocaleString()}</div>
            `;
            notificationHistory.insertBefore(notification, notificationHistory.firstChild);
        });

        // 连接状态
        socket.on('connect', () => {
            console.log('已连接到服务器');
        });

        socket.on('disconnect', () => {
            console.log('与服务器断开连接');
        });
    </script>
</body>
</html> 