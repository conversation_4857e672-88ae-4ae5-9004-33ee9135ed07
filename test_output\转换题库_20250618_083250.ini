填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写"对或错"；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用"/"分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含"/",请以{/}表示以防止系统误判为答案分隔符。示例，如a/b{/}b/c{/}{/}c,表示有三个备选答案"a"、"b/b"、"c//c"。


1.【单选题】（示例导入前请删除）这是一道单选题的题目。       
A、选项
B、选项
C、选项
D、选项
正确答案： A 
题目难度：简单
答案解析：暂无解析
知识点：暂无知识点


2.【多选题】（示例导入前请删除）这是一道多选题的题目。
A、选项
B、选项
C、选项
D、选项
E、选项
F、选项
G、选项
正确答案：AB
题目难度：简单
答案解析：暂无解析
知识点：暂无知识点


3.【判断题】（示例导入前请删除）这是一道判断题的题目。
正确答案：对
题目难度：简单
答案解析：暂无解析
知识点：暂无知识点
4.【填空题】（示例导入前请删除）我是填空题题目，第1个空【备选答案1/备选答案2/备选答案3】,第2个空【备选答案4/备选答案5/备选答案6】 ,填空
题目难度：简单
作答上传图片：否
答案解析：暂无解析
知识点：暂无知识点



5.【简答题】（示例导入前请删除）这是一道问答题的题目。
关键字1：备选答案1/备选答案2/备选答案3
关键字2：备选答案4/备选答案5/备选答案6
关键字3：备选答案7/备选答案8/备选答案9
关键字4：备选答案10/备选答案11/备选答案12
关键字5：备选答案13/备选答案14/备选答案15
题目难度：简单
作答上传图片：否
答案解析：暂无解析
知识点：暂无知识点

1.【单选题】以下哪个是Python的特点？
A、面向对象
B、解释型语言
C、跨平台
D、以上都是
正确答案：D
题目难度：简单
答案解析：Python具有面向对象、解释型、跨平台等特点
知识点：Python基础

2.【多选题】Python中哪些是可变数据类型？
A、列表
B、元组
C、字典
D、集合
正确答案：ACD
题目难度：一般
答案解析：列表、字典、集合是可变的，元组是不可变的
知识点：Python数据类型

3.【判断题】Python是一种编译型语言
正确答案：错
题目难度：简单
答案解析：Python是解释型语言，不是编译型语言
知识点：Python基础

4.【填空题】Python中用于定义函数的关键字是【def】
题目难度：简单
作答上传图片：否
答案解析：def是Python中定义函数的关键字
知识点：Python函数

5.【简答题】请简述Python的主要优势
关键字1：简洁易读/简洁易读
关键字2：跨平台/跨平台
关键字3：丰富的库/丰富的库
关键字4：开源免费/开源免费
题目难度：一般
作答上传图片：否
答案解析：Python具有语法简洁、跨平台、库丰富、开源等优势
知识点：Python基础

6.【简答题】列表和元组的区别是什么？
关键字1：可变性/可变性
关键字2：性能/性能
关键字3：用途/用途
题目难度：较难
作答上传图片：否
答案解析：列表是可变的，元组是不可变的；元组性能更好；用途不同
知识点：Python数据类型

7.【填空题】Python中如何创建一个空列表？【[]/list()】
题目难度：简单
作答上传图片：否
答案解析：可以使用[]或list()函数创建空列表
知识点：Python数据类型

8.【判断题】Python支持面向对象编程
正确答案：对
题目难度：简单
答案解析：Python是一种面向对象的编程语言
知识点：Python基础

9.【多选题】下列哪些是Python的内置数据类型？
A、int
B、str
C、list
D、dict
E、tuple
正确答案：ABCDE
题目难度：一般
答案解析：这些都是Python的内置数据类型
知识点：Python数据类型

10.【简答题】Python的缩进有什么作用？
关键字1：代码块标识/代码块标识
关键字2：语法要求/语法要求
关键字3：提高可读性/提高可读性
题目难度：一般
作答上传图片：否
答案解析：Python使用缩进来标识代码块，这是语法要求，同时提高了代码可读性
知识点：Python语法

11.【简答题】简述国能南宁发电有限公司党务公开的指导思想。
关键字1：国能南宁发电有限公司党务公开的指导思想是：坚持以习近平新时代中国特色社会主义思想为指导/国能南宁发电有限公司党务公开的指导思想是：坚持以习近平新时代中国特色社会主义思想为指导
关键字2：以发展党内民主、增进党内和谐、加强党内监督为重点/以发展党内民主、增进党内和谐、加强党内监督为重点
关键字3：尊重党员主体地位/尊重党员主体地位
关键字4：保障党员民主权利/保障党员民主权利
关键字5：营造党内民主环境/营造党内民主环境
关键字6：构建民主开放工作机制/构建民主开放工作机制
关键字7：调动广大党员参与党内事务的积极性/调动广大党员参与党内事务的积极性
关键字8：促进公司党务工作的制度化、规范化和科学化/促进公司党务工作的制度化、规范化和科学化
关键字9：增强党组织的凝聚力和战斗力/增强党组织的凝聚力和战斗力
关键字10：为公司持续健康发展提供坚强有力的组织保证。/为公司持续健康发展提供坚强有力的组织保证。
题目难度：较难
作答上传图片：否
答案解析：根据《国能南宁发电有限公司党务公开管理办法（修订）》第四条规定，详细阐述了党务公开的指导思想。
知识点：

12.【简答题】简述国能南宁发电有限公司党务公开工作领导小组的主要职责。
关键字1：国能南宁发电有限公司党务公开工作领导小组的主要职责是：传达上级关于党务公开工作的有关会议及文件精神，安排部署党务公开各阶段重要工作/国能南宁发电有限公司党务公开工作领导小组的主要职责是：传达上级关于党务公开工作的有关会议及文件精神，安排部署党务公开各阶段重要工作
关键字2：对党务公开工作小组、监督小组的日常工作进行监督、检查、指导，研究解决在工作中遇到的重要问题/对党务公开工作小组、监督小组的日常工作进行监督、检查、指导，研究解决在工作中遇到的重要问题
关键字3：研究讨论党务公开重大事项，严格审核党务公司内容，确保全面、真实。/研究讨论党务公开重大事项，严格审核党务公司内容，确保全面、真实。
题目难度：较难
作答上传图片：否
答案解析：根据《国能南宁发电有限公司党务公开管理办法（修订）》第六条规定，详细阐述了党务公开工作领导小组的主要职责。
知识点：

