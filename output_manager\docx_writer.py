"""
DOCX输出管理模块
负责将INI格式的题目保存为DOCX格式
"""

import os
import logging
from typing import List, Dict, Any
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.shared import OxmlElement, qn

logger = logging.getLogger(__name__)

class DOCXWriter:
    """DOCX文件写入器"""
    
    def __init__(self, config=None):
        """
        初始化DOCX写入器
        
        Args:
            config: 配置对象
        """
        self.config = config
        self.template_header = self._load_docx_header() # 加载DOCX模板头部
        logger.info(f"DOCX写入器初始化完成。")
        
    def _load_docx_header(self) -> str:
        """加载DOCX文件的默认题头"""
        default_header = """填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写"对或错"；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用"/"分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含"/"，请以{/}表示以防止系统误判为答案分隔符。示例，如a/b{/}b/c{/}{/}c,表示有三个备选答案"a"、"b/b"、"c//c"。


"""
        return default_header

    def create_docx_from_ini(self, ini_content: str, filename: str = None) -> str:
        """
        从INI内容创建DOCX文件，并处理题目拆分
        
        Args:
            ini_content: INI格式的内容
            filename: 输出文件名（不包含扩展名，用于生成多个文件）
            
        Returns:
            保存的文件路径（如果是单个文件）或第一个文件的路径（如果是多个文件）
        """
        questions = self._parse_ini_to_questions(ini_content)
        total_questions = len(questions)
        
        if total_questions <= 1000:
            # 如果题目数量不超过1000，直接生成一个文件
            return self._create_single_docx(questions, filename)
        else:
            # 如果题目数量超过1000，进行拆分
            base_filename = filename if filename else f"题库_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 计算需要拆分的份数
            num_files = (total_questions + 999) // 1000 # 向上取整
            
            output_paths = []
            for i in range(num_files):
                start_index = i * 1000
                end_index = min((i + 1) * 1000, total_questions)
                
                # 获取当前分段的题目
                current_questions = questions[start_index:end_idx]
                
                # 生成新的文件名
                part_filename = f"{base_filename}_part{i+1}.docx"
                
                # 递归调用自身生成分段文件
                part_path = self._create_single_docx(current_questions, part_filename)
                output_paths.append(part_path)
                
            logger.info(f"成功将 {total_questions} 道题目拆分为 {num_files} 个DOCX文件。")
            return output_paths[0] if output_paths else "" # 返回第一个文件的路径
            
    def _create_single_docx(self, questions: List[Dict[str, Any]], filename: str = None) -> str:
        """
        创建单个DOCX文件
        """
        if filename is None:
            filename = f"题库_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            
        if not filename.endswith('.docx'):
            filename += '.docx'
            
        file_path = filename
        
        # 确保目标目录存在
        output_dir = os.path.dirname(file_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 创建新文档
            doc = Document()
            
            # 设置文档样式
            self._setup_document_styles(doc)
            
            # 添加题头
            if self.template_header:
                header_lines = self.template_header.split('\n')
                for line in header_lines:
                    para = doc.add_paragraph(line)
            
            # 添加题目内容
            self._parse_and_add_ini_content(doc, questions)
            
            # 保存文档
            doc.save(file_path)
            
            logger.info(f"成功创建DOCX文件: {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"创建DOCX文件失败: {str(e)}")
            raise
            
    def _setup_document_styles(self, doc: Document):
        """设置文档样式"""
        try:
            # 设置默认字体
            style = doc.styles['Normal']
            font = style.font
            font.name = '宋体'
            font.size = Pt(12)
            
            # 设置页边距
            sections = doc.sections
            for section in sections:
                section.top_margin = Inches(1)
                section.bottom_margin = Inches(1)
                section.left_margin = Inches(1)
                section.right_margin = Inches(1)
                
        except Exception as e:
            logger.warning(f"设置文档样式失败: {str(e)}")
            
    def _parse_and_add_ini_content(self, doc: Document, questions: List[Dict[str, Any]]):
        """解析题目列表并添加到文档"""
        for question in questions:
            lines = question["content"].split('\n')
            for line in lines:
                para = doc.add_paragraph(line)
                # 可以选择性地保留一些基本格式，例如空行，或者完全不处理样式
                # 如果需要完全原样保留，可以不设置任何样式
            doc.add_paragraph("") # 每个题目之间添加一个空行

    def _parse_ini_to_questions(self, ini_content: str) -> List[Dict[str, Any]]:
        """
        解析INI内容，将其拆分为独立的题目块。
        每个题目块以数字加点开头，例如 "1.【单选题】"
        """
        questions = []
        current_question_lines = []
        
        lines = ini_content.split('\n')
        for line in lines:
            # 检查是否是新题目的开始（例如 "1.【单选题】"）
            # 使用正则表达式匹配数字加点和【】
            import re
            if re.match(r'^\d+\.【.*?】', line.strip()):
                if current_question_lines:
                    questions.append({"content": "\n".join(current_question_lines).strip()})
                    current_question_lines = []
                current_question_lines.append(line)
            elif line.strip() == "" and current_question_lines: # 遇到空行，如果当前有题目内容，也作为一个分隔
                if current_question_lines:
                    questions.append({"content": "\n".join(current_question_lines).strip()})
                    current_question_lines = []
            else:
                current_question_lines.append(line)
        
        # 添加最后一个题目
        if current_question_lines:
            questions.append({"content": "\n".join(current_question_lines).strip()})
            
        return questions

    def merge_docx_files(self, docx_files: List[str], output_filename: str = None) -> str:
        """
        合并多个DOCX文件
        
        Args:
            docx_files: DOCX文件路径列表
            output_filename: 输出文件名
            
        Returns:
            合并后的文件路径
        """
        if output_filename is None:
            output_filename = f"合并题库_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            
        if not output_filename.endswith('.docx'):
            output_filename += '.docx'
            
        output_path = output_filename # output_filename现在应该包含完整的路径
        
        # 确保目标目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        try:
            # 创建新文档
            merged_doc = Document()
            
            # 设置文档样式
            self._setup_document_styles(merged_doc)
            
            # 添加标题
            title = merged_doc.add_heading('合并题库文档', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 合并文件
            for i, docx_file in enumerate(docx_files):
                if os.path.exists(docx_file):
                    try:
                        source_doc = Document(docx_file)
                        
                        # 添加文件分隔
                        if i > 0:
                            merged_doc.add_page_break()
                            
                        merged_doc.add_heading(f'文件 {i+1}: {os.path.basename(docx_file)}', 1)
                        
                        # 复制段落
                        for para in source_doc.paragraphs:
                            new_para = merged_doc.add_paragraph(para.text)
                            new_para.style = para.style
                            
                        logger.info(f"已合并文件: {docx_file}")
                        
                    except Exception as e:
                        logger.error(f"合并文件 {docx_file} 失败: {str(e)}")
                        continue
                        
            # 保存合并后的文档
            merged_doc.save(output_path)
            
            logger.info(f"成功合并 {len(docx_files)} 个DOCX文件到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"合并DOCX文件失败: {str(e)}")
            raise
