#!/usr/bin/env python3
"""
题库转换功能演示脚本
"""

import os
import sys
import logging # 导入logging模块
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 添加项目根目录到路径
sys.path.append('.')

from converters.csv_reader import CSVReader
from converters.ini_converter import INIConverter
from output_manager.docx_writer import DOCXWriter

def main():
    """主函数"""
    print("=== 题库转换功能演示 ===")
    
    # 输入文件
    input_file = "示例题库.csv" # 假设示例题库.csv在项目根目录
    if not os.path.exists(input_file):
        # 尝试从上级目录查找，因为脚本被移动到test子目录
        input_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), "示例题库.csv")
        if not os.path.exists(input_file):
            print(f"错误: 找不到输入文件 {input_file}")
            return
    
    try:
        # 1. 读取CSV文件
        print(f"\n1. 读取CSV文件: {input_file}")
        reader = CSVReader()
        questions = reader.read_file(input_file)
        print(f"   成功读取 {len(questions)} 道题目")
        
        # 显示列名
        columns = reader.get_column_names(input_file)
        print(f"   列名: {columns}")

        # 打印第一个单选题的选项，用于调试
        for q in questions:
            if q.get('题型') == '单选题':
                print(f"   第一个单选题的原始选项: {q.get('选项')}")
                break
        
        # 2. 设置列名映射
        print(f"\n2. 设置列名映射")
        column_mapping = {
            'question': '题目',
            'type': '题型',
            'options': '选项',
            'answer': '答案',
            'explanation': '解析',
            'difficulty': '难度',
            'knowledge_points': '知识点'
        }
        print(f"   映射关系: {column_mapping}")
        
        # 3. 转换为INI格式
        print(f"\n3. 转换为INI格式")
        converter = INIConverter()
        ini_content = converter.convert_questions_to_ini(questions, column_mapping)
        print(f"   转换完成，生成INI内容长度: {len(ini_content)} 字符")
        
        # 4. 保存INI文件
        print(f"\n4. 保存INI文件")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        ini_filename = f"转换题库_{timestamp}.ini"
        # 将INI文件保存到test_output目录下
        ini_path = converter.save_ini_file(ini_content, os.path.join("test_output", ini_filename))
        print(f"   INI文件已保存: {ini_path}")
        
        # 5. 生成DOCX文件
        print(f"\n5. 生成DOCX文件")
        docx_writer = DOCXWriter()
        docx_filename = f"转换题库_{timestamp}.docx"
        # 将DOCX文件保存到test_output目录下
        docx_path = docx_writer.create_docx_from_ini(ini_content, os.path.join("test_output", docx_filename))
        print(f"   DOCX文件已保存: {docx_path}")
        
        # 6. 处理无法识别的题目
        if converter.unrecognized_questions:
            print(f"\n6. 发现 {len(converter.unrecognized_questions)} 道无法识别的题目")
            unrecognized_filename = f"无法识别题目_{timestamp}.json"
            # 将无法识别题目保存到test_output目录下
            unrecognized_file_path = os.path.join("test_output", unrecognized_filename)
            try:
                with open(unrecognized_file_path, 'w', encoding='utf-8') as f:
                    import json
                    json.dump(converter.unrecognized_questions, f, ensure_ascii=False, indent=4)
                print(f"   无法识别的题目已保存到: {unrecognized_file_path}")
            except Exception as e:
                print(f"   保存无法识别题目失败: {str(e)}")
        else:
            print(f"\n6. 没有发现无法识别的题目")
        
        # 7. 显示转换结果摘要
        print(f"\n=== 转换完成 ===")
        print(f"输入文件: {input_file}")
        print(f"处理题目数: {len(questions)}")
        print(f"输出文件:")
        print(f"  - INI文件: {ini_filename}")
        print(f"  - DOCX文件: {docx_filename}")
        
        # 8. 显示INI内容预览
        print(f"\n=== INI内容预览 ===")
        preview_lines = ini_content.split('\n')[:30]  # 显示前30行
        for line in preview_lines:
            print(line)
        if len(ini_content.split('\n')) > 30:
            print("... (内容已截断)")
            
        # 9. 题型统计
        print(f"\n=== 题型统计 ===")
        type_count = {}
        for question in questions:
            q_type = question.get('题型', '未知')
            type_count[q_type] = type_count.get(q_type, 0) + 1
        
        for q_type, count in type_count.items():
            print(f"  {q_type}: {count} 道")
            
    except Exception as e:
        print(f"转换过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
