"""
题库转换功能测试
"""

import os
import sys
import unittest
import tempfile
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from converters.csv_reader import CSVReader
from converters.ini_converter import INIConverter
from output_manager.docx_writer import DOCXWriter

class TestConverter(unittest.TestCase):
    """题库转换测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        
        # 创建测试CSV数据
        self.test_data = [
            {
                '题目': '以下哪个是Python的特点？',
                '题型': '单选题',
                '选项': 'A、面向对象|B、解释型语言|C、跨平台|D、以上都是',
                '答案': 'D',
                '解析': 'Python具有面向对象、解释型、跨平台等特点',
                '难度': '简单',
                '知识点': 'Python基础'
            },
            {
                '题目': 'Python中哪些是可变数据类型？',
                '题型': '多选题',
                '选项': 'A、列表|B、元组|C、字典|D、集合',
                '答案': 'ACD',
                '解析': '列表、字典、集合是可变的，元组是不可变的',
                '难度': '一般',
                '知识点': 'Python数据类型'
            },
            {
                '题目': 'Python是一种编译型语言',
                '题型': '判断题',
                '选项': '',
                '答案': '错',
                '解析': 'Python是解释型语言，不是编译型语言',
                '难度': '简单',
                '知识点': 'Python基础'
            }
        ]
        
        # 创建测试CSV文件
        self.test_csv_path = os.path.join(self.test_dir, 'test_questions.csv')
        df = pd.DataFrame(self.test_data)
        df.to_csv(self.test_csv_path, index=False, encoding='utf-8-sig')
        
    def tearDown(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
    def test_csv_reader(self):
        """测试CSV读取器"""
        reader = CSVReader()
        
        # 测试读取文件
        questions = reader.read_file(self.test_csv_path)
        self.assertEqual(len(questions), 3)
        self.assertEqual(questions[0]['题目'], '以下哪个是Python的特点？')
        
        # 测试获取列名
        columns = reader.get_column_names(self.test_csv_path)
        expected_columns = ['题目', '题型', '选项', '答案', '解析', '难度', '知识点']
        self.assertEqual(columns, expected_columns)
        
        # 测试预览数据
        preview = reader.preview_data(self.test_csv_path, rows=2)
        self.assertEqual(len(preview), 2)
        
    def test_ini_converter(self):
        """测试INI转换器"""
        reader = CSVReader()
        questions = reader.read_file(self.test_csv_path)
        
        converter = INIConverter()
        
        # 测试列名映射建议
        columns = reader.get_column_names(self.test_csv_path)
        suggestions = converter.get_column_mapping_suggestions(columns)
        
        self.assertIn('question', suggestions)
        self.assertIn('type', suggestions)
        self.assertEqual(suggestions['question'], '题目')
        self.assertEqual(suggestions['type'], '题型')
        
        # 测试转换为INI格式
        column_mapping = {
            'question': '题目',
            'type': '题型',
            'options': '选项',
            'answer': '答案',
            'explanation': '解析',
            'difficulty': '难度',
            'knowledge_points': '知识点'
        }
        
        ini_content = converter.convert_questions_to_ini(questions, column_mapping)
        
        # 验证INI内容
        self.assertIn('【单选题】', ini_content)
        self.assertIn('【多选题】', ini_content)
        self.assertIn('【判断题】', ini_content)
        self.assertIn('以下哪个是Python的特点？', ini_content)
        self.assertIn('正确答案：D', ini_content)
        self.assertIn('正确答案：ACD', ini_content)
        self.assertIn('正确答案：错', ini_content)
        
        # 测试保存INI文件
        ini_path = os.path.join(self.test_dir, 'test_output.ini')
        saved_path = converter.save_ini_file(ini_content, ini_path)
        self.assertEqual(saved_path, ini_path)
        self.assertTrue(os.path.exists(ini_path))
        
    def test_docx_writer(self):
        """测试DOCX写入器"""
        # 创建测试INI内容
        ini_content = """将计划填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；

1.【单选题】以下哪个是Python的特点？
A、面向对象
B、解释型语言
C、跨平台
D、以上都是
正确答案：D
题目难度：简单
答案解析：Python具有面向对象、解释型、跨平台等特点
知识点：Python基础

2.【判断题】Python是一种编译型语言
正确答案：错
题目难度：简单
答案解析：Python是解释型语言，不是编译型语言
知识点：Python基础
"""
        
        writer = DOCXWriter()
        writer.output_dir = self.test_dir
        
        # 测试从INI内容创建DOCX
        docx_path = writer.create_docx_from_ini(ini_content, 'test_output.docx')
        self.assertTrue(os.path.exists(docx_path))
        self.assertTrue(docx_path.endswith('.docx'))
        
        # 测试从题目列表创建DOCX
        questions = [
            {
                'question': '什么是Python？',
                'type': '简答题',
                'answer': '一种编程语言',
                'difficulty': '简单',
                'explanation': 'Python是一种高级编程语言'
            }
        ]
        
        docx_path2 = writer.create_docx_from_questions(questions, 'test_questions.docx')
        self.assertTrue(os.path.exists(docx_path2))
        
    def test_integration(self):
        """集成测试：完整的转换流程"""
        # 1. 读取CSV
        reader = CSVReader()
        questions = reader.read_file(self.test_csv_path)
        
        # 2. 转换为INI
        converter = INIConverter()
        column_mapping = {
            'question': '题目',
            'type': '题型',
            'options': '选项',
            'answer': '答案',
            'explanation': '解析',
            'difficulty': '难度',
            'knowledge_points': '知识点'
        }
        
        ini_content = converter.convert_questions_to_ini(questions, column_mapping)
        
        # 3. 保存INI文件
        ini_path = os.path.join(self.test_dir, 'integration_test.ini')
        converter.save_ini_file(ini_content, ini_path)
        
        # 4. 生成DOCX文件
        writer = DOCXWriter()
        writer.output_dir = self.test_dir
        docx_path = writer.create_docx_from_ini(ini_content, 'integration_test.docx')
        
        # 验证结果
        self.assertTrue(os.path.exists(ini_path))
        self.assertTrue(os.path.exists(docx_path))
        
        # 验证文件内容
        with open(ini_path, 'r', encoding='utf-8') as f:
            content = f.read()
            self.assertIn('【单选题】', content)
            self.assertIn('【多选题】', content)
            self.assertIn('【判断题】', content)

if __name__ == '__main__':
    unittest.main()
