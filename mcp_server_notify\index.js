const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const notifier = require('node-notifier');
const path = require('path');

class MCPServer {
    constructor(port = 3000) {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server);
        this.port = port;
        
        this.setupRoutes();
        this.setupSocketIO();
    }

    setupRoutes() {
        // 提供静态文件
        this.app.use(express.static(path.join(__dirname, 'public')));
        
        // 健康检查端点
        this.app.get('/health', (req, res) => {
            res.json({ status: 'ok' });
        });
    }

    setupSocketIO() {
        this.io.on('connection', (socket) => {
            console.log('客户端已连接');

            socket.on('notify', (data) => {
                this.sendNotification(data);
            });

            socket.on('disconnect', () => {
                console.log('客户端已断开连接');
            });
        });
    }

    sendNotification(data) {
        const { title, message, sound = true } = data;
        
        // 发送桌面通知
        notifier.notify({
            title: title || 'MCP通知',
            message: message || '任务已完成',
            sound: sound,
            wait: true
        });

        // 广播通知给所有连接的客户端
        this.io.emit('notification', {
            title: title || 'MCP通知',
            message: message || '任务已完成',
            timestamp: new Date().toISOString()
        });
    }

    start() {
        this.server.listen(this.port, () => {
            console.log(`MCP通知服务器运行在端口 ${this.port}`);
            console.log(`访问 http://localhost:${this.port} 查看状态`);
        });
    }
}

// 如果直接运行此文件
if (require.main === module) {
    const server = new MCPServer();
    server.start();
}

module.exports = MCPServer; 