"""
INI格式转换器
将题目数据转换为INI模板格式
"""

import os
import logging
import configparser # 导入configparser模块
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class INIConverter:
    """INI格式转换器"""
    
    def __init__(self, template_path: str = None):
        """
        初始化转换器
        
        Args:
            template_path: INI模板文件路径
        """
        self.template_path = template_path or "新模板.ini"
        self.question_counter = 1
        self.unrecognized_questions = []
        self.template_header = self._load_template_header() # 加载模板头部
        
        # 题型映射
        self.question_type_mapping = {
            '单选题': '单选题',
            '多选题': '多选题', 
            '判断题': '判断题',
            '填空题': '填空题',
            '简答题': '简答题',
            '问答题': '简答题',
            '问答': '简答题',
            '单选': '单选题',
            '多选': '多选题',
            '判断': '判断题',
            '填空': '填空题',
            '简答': '简答题'
        }
        
        # 难度映射
        self.difficulty_mapping = {
            '简单': '简单',
            '一般': '一般', 
            '较难': '较难',
            '困难': '困难',
            '低': '简单',
            '中': '一般',
            '高': '较难',
            '1': '简单',
            '2': '一般',
            '3': '较难',
            '4': '困难'
        }
        
    def convert_questions_to_ini(self, questions: List[Dict[str, Any]], 
                                column_mapping: Dict[str, str]) -> str:
        """
        将题目数据转换为INI格式
        
        Args:
            questions: 题目数据列表
            column_mapping: 列名映射关系
            
        Returns:
            INI格式的字符串
        """
        try:
            # 读取模板头部
            ini_content = self._get_template_header()
            
            # 重置题目计数器
            self.question_counter = 1
            
            # 转换每个题目
            for question in questions:
                try:
                    question_ini = self._convert_single_question(question, column_mapping)
                    if question_ini:
                        ini_content += question_ini + "\n\n"
                        self.question_counter += 1
                except Exception as e:
                    logger.warning(f"转换第 {self.question_counter} 题失败: {str(e)}")
                    self.unrecognized_questions.append(question)
                    continue
                    
            logger.info(f"成功转换 {self.question_counter - 1} 道题目为INI格式")
            return ini_content
            
        except Exception as e:
            logger.error(f"转换INI格式失败: {str(e)}")
            raise
            
    def _get_template_header(self) -> str:
        """获取模板头部信息"""
        return self.template_header

    def _load_template_header(self) -> str:
        """从INI文件加载模板头部信息，如果失败则返回硬编码的默认值"""
        default_header = """填写规范（请勿删除）：
1.题型：支持单选题、多选题、判断题、填空题、简答题，用"【】"标识，如【单选题】；
2.题目难度：简单/一般/较难/困难；
3.知识点：最多支持五个知识点，多个知识点用"|"分隔，每个知识点最多20个字；
4.单选、多选题：选项个数最多支持12个：A B C D E F G H I J K L（多余选项系统自动忽略）；正确答案请填写大写A B C D E F G H I J K L；
5.判断题：正确答案填写"对或错"；
6.填空题：题目中用【】表示一个空，最多支持12个空（多余的空系统自动忽略），每个空可设置3个备选答案，多个备选答案用"/"分隔：
7.简答题：关键词用作系统阅卷使用，最多支持12个（多余关键词系统自动忽略）；
8.请尽量避免特殊字符输入（表情、乱码），以免影响系统校验；
9.题目和选项上图片不支持模板导入，请导入试题后在系统通过编辑试题插入图片；
10.如果填空题和简答题答案中包含"/"，请以{/}表示以防止系统误判为答案分隔符。示例，如a/b{/}b/c{/}{/}c,表示有三个备选答案"a"、"b/b"、"c//c"。


"""
        try:
            if not os.path.exists(self.template_path):
                logger.warning(f"INI模板文件不存在: {self.template_path}，将使用默认头部。")
                return default_header

            config = configparser.ConfigParser(allow_no_value=True, strict=False)
            # 使用read_string来处理可能没有节的INI文件
            with open(self.template_path, 'r', encoding='utf-8') as f:
                ini_string = f.read()
            
            # 为了兼容没有section的INI文件，暂时包装一下
            config.read_string('[DEFAULT]\n' + ini_string)
            
            # 尝试获取一个特殊的键，或者直接返回整个内容
            # 由于INI文件可能没有标准Section，我们直接读取文件内容作为头部
            with open(self.template_path, 'r', encoding='utf-8') as f:
                header_content = f.read()
            logger.info(f"成功从 {self.template_path} 加载模板头部。")
            return header_content
        except Exception as e:
            logger.error(f"加载INI模板文件 {self.template_path} 失败: {str(e)}，将使用默认头部。")
            return default_header
        
    def _convert_single_question(self, question: Dict[str, Any], 
                                column_mapping: Dict[str, str]) -> str:
        """
        转换单个题目
        
        Args:
            question: 题目数据
            column_mapping: 列名映射
            
        Returns:
            INI格式的题目字符串
        """
        try:
            # 获取映射后的字段值
            question_text = question.get(column_mapping.get('question', ''), '').strip()
            question_type = question.get(column_mapping.get('type', ''), '').strip()
            options = question.get(column_mapping.get('options', ''), '').strip()
            answer = question.get(column_mapping.get('answer', ''), '').strip()
            explanation = question.get(column_mapping.get('explanation', ''), '暂无解析').strip()
            difficulty = question.get(column_mapping.get('difficulty', ''), '简单').strip()
            knowledge_points = question.get(column_mapping.get('knowledge_points', ''), '暂无知识点').strip()
            
            logger.debug(f"处理题目 {self.question_counter}: 题目='{question_text}', 题型='{question_type}', 答案='{answer}'")

            # 验证必填字段
            if not question_text or not question_type:
                logger.warning(f"题目 {self.question_counter} 缺少必填字段")
                self.unrecognized_questions.append(question)
                return ""
                
            # 标准化题型
            question_type = self._normalize_question_type(question_type)
            if not question_type:
                logger.warning(f"题目 {self.question_counter} 题型无法识别")
                self.unrecognized_questions.append(question)
                return ""
                
            # 标准化难度
            difficulty = self._normalize_difficulty(difficulty)
            
            # 根据题型生成INI内容
            if question_type == '单选题':
                return self._format_single_choice(question_text, options, answer, 
                                                explanation, difficulty, knowledge_points)
            elif question_type == '多选题':
                return self._format_multiple_choice(question_text, options, answer,
                                                  explanation, difficulty, knowledge_points)
            elif question_type == '判断题':
                return self._format_true_false(question_text, answer,
                                             explanation, difficulty, knowledge_points)
            elif question_type == '填空题':
                return self._format_fill_blank(question_text, answer,
                                             explanation, difficulty, knowledge_points)
            elif question_type == '简答题':
                return self._format_short_answer(question_text, answer,
                                               explanation, difficulty, knowledge_points)
            else:
                logger.warning(f"不支持的题型: {question_type}")
                self.unrecognized_questions.append(question)
                return ""
                
        except Exception as e:
            logger.error(f"转换题目失败: {str(e)}")
            self.unrecognized_questions.append(question)
            return ""
            
    def _normalize_question_type(self, question_type: str) -> str:
        """标准化题型"""
        question_type = question_type.strip()
        return self.question_type_mapping.get(question_type, '')
        
    def _normalize_difficulty(self, difficulty: str) -> str:
        """标准化难度"""
        difficulty = difficulty.strip()
        return self.difficulty_mapping.get(difficulty, '简单')
        
    def _format_single_choice(self, question_text: str, options: str, answer: str,
                             explanation: str, difficulty: str, knowledge_points: str) -> str:
        """格式化单选题"""
        ini_text = f"{self.question_counter}.【单选题】{question_text}\n"
        
        # 处理选项
        option_lines = self._parse_options(options)
        ini_text += "\n".join(option_lines) + "\n"
        
        # 处理答案
        answer = self._normalize_answer(answer, 'single')
        ini_text += f"正确答案：{answer}\n"
        ini_text += f"题目难度：{difficulty}\n"
        ini_text += f"答案解析：{explanation}\n"
        ini_text += f"知识点：{knowledge_points}"
        
        return ini_text
        
    def _format_multiple_choice(self, question_text: str, options: str, answer: str,
                               explanation: str, difficulty: str, knowledge_points: str) -> str:
        """格式化多选题"""
        ini_text = f"{self.question_counter}.【多选题】{question_text}\n"
        
        # 处理选项
        option_lines = self._parse_options(options)
        ini_text += "\n".join(option_lines) + "\n"
        
        # 处理答案
        answer = self._normalize_answer(answer, 'multiple')
        ini_text += f"正确答案：{answer}\n"
        ini_text += f"题目难度：{difficulty}\n"
        ini_text += f"答案解析：{explanation}\n"
        ini_text += f"知识点：{knowledge_points}"
        
        return ini_text
        
    def _format_true_false(self, question_text: str, answer: str,
                          explanation: str, difficulty: str, knowledge_points: str) -> str:
        """格式化判断题"""
        ini_text = f"{self.question_counter}.【判断题】{question_text}\n"
        
        # 处理答案
        answer = self._normalize_answer(answer, 'true_false')
        ini_text += f"正确答案：{answer}\n"
        ini_text += f"题目难度：{difficulty}\n"
        ini_text += f"答案解析：{explanation}\n"
        ini_text += f"知识点：{knowledge_points}"
        
        return ini_text
        
    def _format_fill_blank(self, question_text: str, answer: str,
                          explanation: str, difficulty: str, knowledge_points: str) -> str:
        """格式化填空题"""
        # 处理填空题的特殊格式
        formatted_question = self._format_fill_blank_question(question_text, answer)
        
        ini_text = f"{self.question_counter}.【填空题】{formatted_question}\n"
        ini_text += f"题目难度：{difficulty}\n"
        ini_text += f"作答上传图片：否\n"
        ini_text += f"答案解析：{explanation}\n"
        ini_text += f"知识点：{knowledge_points}"
        
        return ini_text
        
    def _format_short_answer(self, question_text: str, answer: str,
                            explanation: str, difficulty: str, knowledge_points: str) -> str:
        """格式化简答题"""
        ini_text = f"{self.question_counter}.【简答题】{question_text}\n"
        
        # 处理关键词
        keywords = self._parse_keywords(answer)
        for i, keyword in enumerate(keywords, 1):
            ini_text += f"关键字{i}：{keyword}\n"
            
        ini_text += f"题目难度：{difficulty}\n"
        ini_text += f"作答上传图片：否\n"
        ini_text += f"答案解析：{explanation}\n"
        ini_text += f"知识点：{knowledge_points}"
        
        return ini_text

    def _parse_options(self, options: str) -> List[str]:
        """解析选项"""
        if not options:
            return []

        # 支持多种分隔符
        separators = ['|', '\n', ';', '；']
        option_list = [options]

        for sep in separators:
            if sep in options:
                option_list = options.split(sep)
                break

        # 格式化选项
        formatted_options = []
        for i, option in enumerate(option_list):
            option = option.strip()
            if option:
                letter = chr(65 + i)  # A, B, C, D...
                if not option.startswith(letter + '.') and not option.startswith(letter + '、'):
                    option = f"{letter}、{option}"
                formatted_options.append(option)

        return formatted_options

    def _normalize_answer(self, answer: str, question_type: str) -> str:
        """标准化答案"""
        answer = answer.strip()

        if question_type == 'single':
            # 单选题答案标准化
            if len(answer) == 1 and answer.upper() in 'ABCDEFGHIJKL':
                return answer.upper()
            elif answer.lower() in ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l']:
                return answer.upper()
            else:
                # 尝试从答案中提取字母
                import re
                match = re.search(r'[A-La-l]', answer)
                return match.group().upper() if match else 'A'

        elif question_type == 'multiple':
            # 多选题答案标准化
            import re
            letters = re.findall(r'[A-La-l]', answer.upper())
            return ''.join(sorted(set(letters)))

        elif question_type == 'true_false':
            # 判断题答案标准化
            true_values = ['对', '正确', '是', 'true', 't', '1', 'yes', 'y']
            false_values = ['错', '错误', '否', 'false', 'f', '0', 'no', 'n']

            answer_lower = answer.lower()
            if any(val in answer_lower for val in true_values):
                return '对'
            elif any(val in answer_lower for val in false_values):
                return '错'
            else:
                return '对'  # 默认为对

        return answer

    def _format_fill_blank_question(self, question_text: str, answer: str) -> str:
        """格式化填空题题目"""
        # 将答案按分隔符分割
        answers = []
        if '|' in answer:
            answers = answer.split('|')
        elif '/' in answer:
            answers = answer.split('/')
        else:
            answers = [answer]

        # 清理答案
        answers = [ans.strip() for ans in answers if ans.strip()]

        # 如果题目中没有【】标记，尝试自动添加
        if '【】' not in question_text and answers:
            # 简单的填空位置检测
            import re
            # 查找可能的填空位置（下划线、空格等）
            question_text = re.sub(r'_{2,}', '【】', question_text)
            question_text = re.sub(r'\s{3,}', '【】', question_text)

        # 如果还是没有填空标记，在末尾添加
        if '【】' not in question_text and answers:
            question_text += '【】'

        # 为每个填空添加备选答案
        blank_count = question_text.count('【】')
        if blank_count > 0 and answers:
            # 将答案分配给填空
            answers_per_blank = len(answers) // blank_count if blank_count > 0 else 1

            for i in range(blank_count):
                start_idx = i * answers_per_blank
                end_idx = min((i + 1) * answers_per_blank, len(answers))
                blank_answers = answers[start_idx:end_idx]

                if blank_answers:
                    # 每个空最多3个备选答案
                    blank_answers = blank_answers[:3]
                    answer_text = '/'.join(blank_answers)
                    question_text = question_text.replace('【】', f'【{answer_text}】', 1)

        return question_text

    def _parse_keywords(self, answer: str) -> List[str]:
        """解析简答题关键词"""
        if not answer:
            return ['暂无关键词']

        # 支持多种分隔符
        separators = ['|', '\n', ';', '；', ',', '，']
        keywords = [answer]

        for sep in separators:
            if sep in answer:
                keywords = answer.split(sep)
                break

        # 清理关键词
        keywords = [kw.strip() for kw in keywords if kw.strip()]

        # 限制关键词数量（最多12个）
        keywords = keywords[:12]

        # 如果没有关键词，返回默认值
        if not keywords:
            keywords = ['暂无关键词']

        # 为每个关键词添加备选答案格式
        formatted_keywords = []
        for keyword in keywords:
            if '/' not in keyword:
                # 如果没有备选答案，根据规则生成备选答案
                if len(keyword) > 1:
                    formatted_keywords.append(f"{keyword}/{keyword[:-1]}")
                else:
                    formatted_keywords.append(f"{keyword}/{keyword}")
            else:
                formatted_keywords.append(keyword)

        return formatted_keywords

    def save_ini_file(self, ini_content: str, output_path: str) -> str:
        """
        保存INI文件

        Args:
            ini_content: INI内容
            output_path: 输出路径

        Returns:
            保存的文件路径
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(ini_content)

            logger.info(f"INI文件已保存到: {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"保存INI文件失败: {str(e)}")
            raise

    def get_column_mapping_suggestions(self, columns: List[str]) -> Dict[str, str]:
        """
        获取列名映射建议

        Args:
            columns: 文件列名列表

        Returns:
            建议的映射关系
        """
        suggestions = {}

        # 题目内容映射
        question_keywords = ['题目', '题干', 'question', 'content', '内容']
        for col in columns:
            if any(keyword in col.lower() for keyword in question_keywords):
                suggestions['question'] = col
                break

        # 题型映射
        type_keywords = ['题型', 'type', '类型']
        for col in columns:
            if any(keyword in col.lower() for keyword in type_keywords):
                suggestions['type'] = col
                break

        # 选项映射
        option_keywords = ['选项', 'option', 'choices']
        for col in columns:
            if any(keyword in col.lower() for keyword in option_keywords):
                suggestions['options'] = col
                break

        # 答案映射
        answer_keywords = ['答案', 'answer', '正确答案']
        for col in columns:
            if any(keyword in col.lower() for keyword in answer_keywords):
                suggestions['answer'] = col
                break

        # 解析映射
        explanation_keywords = ['解析', 'explanation', '解释']
        for col in columns:
            if any(keyword in col.lower() for keyword in explanation_keywords):
                suggestions['explanation'] = col
                break

        # 难度映射
        difficulty_keywords = ['难度', 'difficulty', '难易度']
        for col in columns:
            if any(keyword in col.lower() for keyword in difficulty_keywords):
                suggestions['difficulty'] = col
                break

        # 知识点映射
        knowledge_keywords = ['知识点', 'knowledge', '知识']
        for col in columns:
            if any(keyword in col.lower() for keyword in knowledge_keywords):
                suggestions['knowledge_points'] = col
                break

        return suggestions
